{"cSpell.words": ["nuxt", "nuxtjs"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.addMissingImports.ts": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.ts": "always", "source.organizeImports": "always", "source.removeUnusedImports": "explicit", "source.sortImports": "always"}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": "on"}, "tailwindCSS.classAttributes": ["class", "ui"], "tailwindCSS.experimental.classRegex": [["ui:\\s*{([^)]*)\\s*}", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}