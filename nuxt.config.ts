// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-07-15",
  devtools: { enabled: true },
  modules: ["@nuxt/ui", "@nuxt/content"],
  css: ["~/assets/css/main.css"],
  content: {
    build: {
      markdown: {
        highlight: {
          theme: {
            default: "github-light",
            dark: "github-dark",
            sepia: "monokai",
          },
          langs: [
            "json",
            "js",
            "ts",
            "html",
            "css",
            "vue",
            "shell",
            "mdc",
            "md",
            "yaml",
            "c",
            "cpp",
            "pascal",
            "javascript",
            "typescript",
            "java",
            "python",
            "go",
            "rust",
            "zig",
            "nix",
            "ps",
            "bash",
            "ps1",
            "shell",
            "fish",
            "sh",
          ],
        },
      },
    },
  },
  ui: {},
});
