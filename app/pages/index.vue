<!-- <script setup lang="ts"></script>

<template>
  <main
    class="flex-1 p-5 w-full flex flex-col gap-y-5 items-center justify-center"
  >
    <p
      class="text-7xl font-bold text-cyan-600 dark:text-cyan-500 hover:bg-amber-300 transition-all duration-300"
    >
      稳健 IT 社
    </p>
    <p class="text-2xl text-neutral">创造发生的地方</p>
  </main>
</template> -->

<script setup lang="ts">
import { onBeforeUnmount, onMounted, reactive } from "vue";

interface Circle {
  id: number;
  x: number; // 百分比
  y: number; // 百分比
  size: number; // vw
  dx: number; // 移动速度
  dy: number; // 移动速度
  ds: number; // 尺寸变化速度
}

const CIRCLE_COUNT = 6;
// 修改为 reactive
const circles = reactive<Circle[]>([]);
let timer: number | undefined;

function random(min: number, max: number) {
  return Math.random() * (max - min) + min;
}

function createCircle(id: number): Circle {
  return {
    id,
    x: random(0, 80),
    y: random(0, 80),
    size: random(2, 48),
    dx: random(-0.15, 0.15),
    dy: random(-0.15, 0.15),
    ds: random(-0.05, 0.05),
  };
}

function circleStyle(c: Circle) {
  return {
    left: `${c.x}vw`,
    top: `${c.y}vh`,
    width: `${c.size}vw`,
    height: `${c.size}vw`,
    transition: "all 1s cubic-bezier(.4,0,.2,1)",
  };
}

function animate() {
  circles.forEach((c) => {
    c.x += c.dx;
    c.y += c.dy;
    c.size += c.ds;
    // 边界处理
    if (c.x < 0 || c.x > 80) c.dx *= -1;
    if (c.y < 0 || c.y > 80) c.dy *= -1;
    if (c.size < 12 || c.size > 32) c.ds *= -1;
  });
}

onMounted(() => {
  // 用 circles.length = 0 清空数组，然后 push
  circles.length = 0;
  for (let i = 0; i < CIRCLE_COUNT; i++) {
    circles.push(createCircle(i));
  }
  timer = window.setInterval(() => {
    animate();
  }, 1200);
});

onBeforeUnmount(() => {
  if (timer) window.clearInterval(timer);
});
</script>

<template>
  <!-- 背景圆形层 -->
  <div class="absolute inset-0 -z-10 blur-2xl overflow-clip">
    <div
      v-for="circle in circles"
      :key="circle.id"
      :style="circleStyle(circle)"
      class="absolute rounded-full bg-amber-500 opacity-40 blur-2xl transition-all duration-1000"
    ></div>
  </div>
  <!-- 页面内容 -->
  <main
    class="flex-1 p-5 w-full flex flex-col gap-y-5 items-center justify-center"
  >
    <p class="text-7xl font-bold text-cyan-600 dark:text-cyan-500">
      稳健 IT 社
    </p>
    <p class="text-2xl text-neutral">创造发生的地方</p>
  </main>
</template>
