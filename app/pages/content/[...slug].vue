<script setup lang="ts">
const route = useRoute();

const { data: home } = await useAsyncData(() =>
  queryCollection("content")
    .path(route.params!.slug as string)
    .first()
);

useSeoMeta({
  title: home.value?.title,
  description: home.value?.description,
});
</script>

<template>
  <ContentRenderer
    v-if="home"
    :value="home"
    class="prose dark:prose-invert prose-pre:bg-neutral-300 dark:prose-pre:bg-neutral-800"
  />
  <div v-else>Home not found</div>
</template>
