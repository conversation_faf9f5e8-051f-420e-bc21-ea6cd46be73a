<template>
  <div class="flex flex-col min-h-dvh">
    <header
      class="w-full p-3 h-15 border-b-1 border-b-neutral flex justify-between sticky top-0 left-0 backdrop-blur-md"
    >
      <div class="flex gap-x-5 items-center">
        <NuxtLink to="/" class="flex items-end gap-x-2 ml-3">
          <UIcon name="i-lucide-home" class="size-5 m-auto" />
          <p class="text-xl font-bold">稳健 IT</p>
        </NuxtLink>
        <NuxtLink to="/content"><p class="text-md">Blogs</p></NuxtLink>
      </div>
      <ClientOnly>
        <UButton
          :icon="isDark ? 'i-lucide-moon' : 'i-lucide-sun'"
          color="neutral"
          variant="ghost"
          @click="isDark = !isDark"
        />
      </ClientOnly>
    </header>
    <slot />
  </div>
</template>

<script lang="ts" setup>
const colorMode = useColorMode();

const isDark = computed({
  get() {
    return colorMode.value === "dark";
  },
  set(_isDark) {
    colorMode.preference = _isDark ? "dark" : "light";
  },
});
</script>
