{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/content": "3.6.3", "@nuxt/ui": "3.3.0", "@tailwindcss/typography": "^0.5.16", "better-sqlite3": "^12.2.0", "nuxt": "^4.0.3", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vue": "^3.5.18", "vue-router": "^4.5.1"}}